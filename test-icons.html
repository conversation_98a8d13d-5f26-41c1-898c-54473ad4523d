<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图标测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .video-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 12px;
            background: white;
        }
        .video-info {
            flex: 1;
            margin-right: 12px;
        }
        .video-title {
            font-weight: 500;
            color: #374151;
            margin-bottom: 4px;
        }
        .video-url {
            font-size: 12px;
            color: #6b7280;
        }
        .actions {
            display: flex;
            gap: 8px;
        }
        .action-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 6px;
            background: #f9fafb;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .action-btn:hover {
            background: #f3f4f6;
        }
        .toolbar {
            display: flex;
            justify-content: center;
            gap: 12px;
            padding: 16px;
            background: #f9fafb;
            border-radius: 8px;
            margin-top: 20px;
        }
        .toolbar-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background: white;
            cursor: pointer;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            transition: background-color 0.2s;
        }
        .toolbar-btn:hover {
            background: #f3f4f6;
        }
        h2 {
            color: #374151;
            margin-bottom: 16px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Snapany Extension 图标测试</h1>
        
        <h2>VideoItem 组件图标</h2>
        <div class="video-item">
            <div class="video-info">
                <div class="video-title">示例视频标题</div>
                <div class="video-url">https://example.com/video.mp4</div>
            </div>
            <div class="actions">
                <button class="action-btn" title="复制链接">
                    <!-- FileCopy 图标位置 -->
                    📋
                </button>
                <button class="action-btn" title="预览视频">
                    <!-- Play 图标位置 -->
                    ▶️
                </button>
                <button class="action-btn" title="下载">
                    <!-- Download 图标位置 -->
                    ⬇️
                </button>
            </div>
        </div>

        <div class="video-item">
            <div class="video-info">
                <div class="video-title">复制成功状态示例</div>
                <div class="video-url">https://example.com/video2.mp4</div>
            </div>
            <div class="actions">
                <button class="action-btn" title="已复制">
                    <!-- Check 图标位置 -->
                    ✅
                </button>
                <button class="action-btn" title="预览视频">
                    <!-- Play 图标位置 -->
                    ▶️
                </button>
                <button class="action-btn" title="下载">
                    <!-- Download 图标位置 -->
                    ⬇️
                </button>
            </div>
        </div>

        <h2>BottomToolbar 组件图标</h2>
        <div class="toolbar">
            <button class="toolbar-btn" title="切换到侧边栏模式">
                <!-- IconLayoutSidebarRightCollapse 图标位置 -->
                📱
            </button>
            <button class="toolbar-btn" title="切换到弹窗模式">
                <!-- IconLayoutNavbarCollapse 图标位置 -->
                🖥️
            </button>
            <button class="toolbar-btn" title="清空记录">
                <!-- IconTrash 图标位置 -->
                🗑️
            </button>
        </div>

        <h2>图标替换说明</h2>
        <ul>
            <li><strong>FileCopy</strong>: 替换了复制按钮的SVG图标</li>
            <li><strong>Play</strong>: 替换了播放按钮的SVG图标</li>
            <li><strong>Check</strong>: 替换了复制成功状态的SVG图标</li>
            <li><strong>Download</strong>: 替换了下载按钮的SVG图标</li>
            <li><strong>IconLayoutSidebarRightCollapse</strong>: 替换了切换到侧边栏的SVG图标</li>
            <li><strong>IconLayoutNavbarCollapse</strong>: 替换了切换到弹窗的SVG图标</li>
            <li><strong>IconTrash</strong>: 替换了清空按钮的SVG图标</li>
        </ul>

        <h2>使用的图标库</h2>
        <ul>
            <li><strong>flowbite-react-icons</strong>: FileCopy, Play, Check, Download</li>
            <li><strong>@tabler/icons-react</strong>: IconLayoutSidebarRightCollapse, IconLayoutNavbarCollapse, IconTrash</li>
        </ul>
    </div>
</body>
</html>
