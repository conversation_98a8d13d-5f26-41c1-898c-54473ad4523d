// 数据转换工具函数

import type { FilteredRequestInfo } from "@/types/network"
import type { VideoData } from "@/types/video"
import { formatFileSize } from "./mediaUtils"



/**
 * 从响应头提取文件扩展名并转换为大写格式
 */
function getFileExtension(headers: chrome.webRequest.HttpHeader[] | undefined, url: string): string {
  if (url.includes('.m3u8')) {
    return 'M3U8'
  }

  // 检查headers是否存在
  if (!headers || !Array.isArray(headers)) {
    return '未知格式'
  }

  const contentTypeHeader = headers.find(
    header => header.name.toLowerCase() === 'content-type'
  )
  if (contentTypeHeader?.value) {
    const contentTypeMatch = contentTypeHeader.value.match(/^[^;]+/)
    if (contentTypeMatch && contentTypeMatch[0]) {
      const contentType = contentTypeMatch[0].toUpperCase()
      if (contentType.includes('/')) {
        return contentType.split('/')[1]
      }
      return contentType
    }
  }
  return '未知格式'
}

/**
 * 生成文件标题（从URL或页面标题）
 */
function generateTitle(request: FilteredRequestInfo): string {
  // 优先从URL提取文件名
  try {
    const url = new URL(request.url)
    const pathname = url.pathname
    let filename = pathname.split('/').pop() || ''

    // 保留扩展名，只清理文件名部分
    filename = filename
      .replace(/[<>:"/\\|?*]/g, '') // 只移除Windows文件名不允许的字符，保留点号用于扩展名
      .replace(/\s+/g, ' ')
      .trim()

    if (filename && filename.length > 0) {
      return filename
    }
  } catch {
    // URL解析失败，继续使用页面标题
  }

  // 如果URL提取不到，使用页面标题并添加扩展名
  let title = (request.pageTitle || 'download')
    .replace(/[<>:"/\\|?*]/g, '') // 只移除Windows文件名不允许的字符
    .replace(/\s+/g, ' ') // 多个空格合并为一个
    .trim()

  // 如果清理后的标题为空，使用默认名称
  if (!title) {
    title = 'download'
  }

  const ext = getExtensionFromContentType(request)

  if (ext) {
    return `${title}.${ext}`
  }

  // 如果没有扩展名，尝试从URL中提取
  try {
    const url = new URL(request.url)
    const pathname = url.pathname
    const urlExt = pathname.split('.').pop()
    if (urlExt && urlExt.length <= 5 && urlExt !== pathname) {
      return `${title}.${urlExt}`
    }
  } catch {
    // URL解析失败，忽略
  }

  return title
}

/**
 * 从content-type获取扩展名的函数
 */
function getExtensionFromContentType(request: FilteredRequestInfo): string | null {
  // 从响应头中获取content-type
  const contentTypeHeader = request.responseHeaders?.find(
    header => header.name?.toLowerCase() === 'content-type'
  )

  if (!contentTypeHeader?.value) {
    return null
  }

  // 提取主要的content-type（去掉参数部分）
  const contentType = contentTypeHeader.value.split(';')[0].trim().toLowerCase()

  // 检查是否包含斜杠
  if (!contentType.includes('/')) {
    return null
  }

  // 提取 / 后面的部分作为扩展名
  let extension = contentType.split('/')[1]

  // 根据过滤规则中的特殊情况进行处理
  switch (contentType) {
    case 'application/vnd.apple.mpegurl':
    case 'application/x-mpegurl':
    case 'application/mpegurl':
    case 'application/octet-stream-m3u8':
      return 'm3u8'
    case 'application/dash+xml':
      return 'mpd'
    case 'application/m4s':
      return 'm4s'
    case 'application/ogg':
      return 'ogg'
    default:
      // 对于标准的 audio/* 和 video/* 类型，直接使用 / 后的部分
      return extension || null
  }
}

/**
 * 从响应头获取文件大小
 */
function getFileSizeFromHeaders(headers?: chrome.webRequest.HttpHeader[]): number | undefined {
  if (!headers) return undefined

  // 查找Content-Range头
  const contentRangeHeader = headers.find(
    header => header.name.toLowerCase() === 'content-range'
  )
  if (contentRangeHeader?.value) {
    const rangeMatch = contentRangeHeader.value.match(/bytes\s+\d+-\d+\/(\d+)/)
    if (rangeMatch && rangeMatch[1]) {
      const size = parseInt(rangeMatch[1], 10)
      return isNaN(size) ? undefined : size
    }
  }

  // 查找Content-Length头
  const contentLengthHeader = headers.find(
    header => header.name.toLowerCase() === 'content-length'
  )

  if (contentLengthHeader?.value) {
    const size = parseInt(contentLengthHeader.value, 10)
    return isNaN(size) ? undefined : size
  }

  return undefined
}

/**
 * 将 FilteredRequestInfo 转换为 VideoData
 */
export function convertToVideoData(request: FilteredRequestInfo): VideoData {
  const fileSize = getFileSizeFromHeaders(request.responseHeaders)
  const extension = getFileExtension(request.responseHeaders, request.url)
  const favIconUrl = request.favIconUrl
  const title = generateTitle(request)

  // 改进文件大小显示格式
  const sizeDisplay = fileSize ? formatFileSize(fileSize) : '未知大小'

  const ext = request.ext || extension

  return {
    title,
    size: `${extension} * ${sizeDisplay}`, // 大小 * 格式
    url: request.url,
    favIconUrl,
    status: request.status === 'completed' ? 'completed' : 'downloading',
    requestHeaders: request.requestHeaders,
    domain: request.domain,
    pageTitle: request.pageTitle,
    pageUrl: request.pageUrl,
    ext: ext,
    requestId: request.requestId
  }
}

/**
 * 将 FilteredRequestInfo 数组转换为 VideoData 数组
 */
export function convertRequestsToVideoData(requests: FilteredRequestInfo[]): VideoData[] {
  return requests.map(convertToVideoData)
} 